import os
import io
from unittest.mock import patch, AsyncMock, MagicMock
from httpx import ASGITransport, AsyncClient
import pytest
from PIL import Image

from server.app import app


def create_test_image() -> io.BytesIO:
    """Create a test image in memory"""
    # Create a simple RGB image
    img = Image.new('RGB', (100, 100), color='red')
    img_bytes = io.BytesIO()
    img.save(img_bytes, format='JPEG')
    img_bytes.seek(0)

    return img_bytes


@pytest.mark.asyncio
async def test_verify_face_success():
    """Test successful face verification"""
    # Mock the external dependencies
    mock_embeddings = [{"embedding": [0.1] * 512, "is_real": True}]
    mock_qdrant_result = MagicMock()
    mock_qdrant_result.points = [MagicMock()]
    mock_qdrant_result.points[0].payload = {"user_id": "test-user-123"}

    mock_auth_response = MagicMock()
    mock_auth_response.status_code = 200
    mock_auth_response.json.return_value = {
        "token": "test-jwt-token",
        "refreshToken": "test-refresh-token",
        "expiration": "2025-12-31T23:59:59",
        "refreshTokenExpiration": "2025-01-31T23:59:59",
        "user": {
            "id": "test-user-123",
            "email": "<EMAIL>",
            "firstName": "John",
            "lastName": "Doe",
            "position": "Cashier",
            "department": {"id": "dept-1", "name": "Store Operations"},
            "departmentId": "dept-1"
        }
    }

    with patch('server.deps.get_embeddings', return_value=mock_embeddings), \
         patch.dict(os.environ, {"API_URL": "http://test-api.com", "AUTH_KEY": "test-key"}), \
         patch('httpx.AsyncClient') as mock_httpx:

        # Setup httpx mock
        mock_client = AsyncMock()
        mock_client.post.return_value = mock_auth_response
        mock_httpx.return_value.__aenter__.return_value = mock_client

        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as client:
            # Mock Qdrant client
            with patch.object(app.state, 'qdrant_client') as mock_qdrant:
                mock_qdrant.query_points.return_value = mock_qdrant_result

                test_image = create_test_image()
                response = await client.post(
                    "/api/verify-face",
                    files={"image": ("test.jpg", test_image, "image/jpeg")},
                    data={"timeLogged": "2025-01-01T12:00:00"}
                )

                assert response.status_code == 200
                data = response.json()
                assert data["token"] == "test-jwt-token"
                assert data["user"]["id"] == "test-user-123"


@pytest.mark.asyncio
async def test_verify_face_multiple_faces_detected():
    """Test error when multiple faces are detected"""
    mock_embeddings = [
        {"embedding": [0.1] * 512, "is_real": True},
        {"embedding": [0.2] * 512, "is_real": True}
    ]

    with patch('server.deps.get_embeddings', return_value=mock_embeddings):
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as client:
            test_image = create_test_image()
            response = await client.post(
                "/api/verify-face",
                files={"image": ("test.jpg", test_image, "image/jpeg")},
                data={"timeLogged": "2025-01-01T12:00:00"}
            )

            assert response.status_code == 400
            data = response.json()
            assert "Multiple faces detected" in data["message"]


@pytest.mark.asyncio
async def test_verify_face_fake_face_detected():
    """Test error when fake face is detected"""
    mock_embeddings = [{"embedding": [0.1] * 512, "is_real": False}]

    with patch('server.deps.get_embeddings', return_value=mock_embeddings):
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as client:
            test_image = create_test_image()
            response = await client.post(
                "/api/verify-face",
                files={"image": ("test.jpg", test_image, "image/jpeg")},
                data={"timeLogged": "2025-01-01T12:00:00"}
            )

            assert response.status_code == 400
            data = response.json()
            assert "Face is not real" in data["message"]


@pytest.mark.asyncio
async def test_verify_face_no_match_found():
    """Test error when no matching face is found in database"""
    mock_embeddings = [{"embedding": [0.1] * 512, "is_real": True}]
    mock_qdrant_result = MagicMock()
    mock_qdrant_result.points = []  # No matches found

    with patch('server.deps.get_embeddings', return_value=mock_embeddings):
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as client:
            with patch.object(app.state, 'qdrant_client') as mock_qdrant:
                mock_qdrant.query_points.return_value = mock_qdrant_result

                test_image = create_test_image()
                response = await client.post(
                    "/api/verify-face",
                    files={"image": ("test.jpg", test_image, "image/jpeg")},
                    data={"timeLogged": "2025-01-01T12:00:00"}
                )

                assert response.status_code == 404
                data = response.json()
                assert "No match found" in data["message"]


@pytest.mark.asyncio
async def test_verify_face_missing_auth_key():
    """Test error when AUTH_KEY environment variable is missing"""
    mock_embeddings = [{"embedding": [0.1] * 512, "is_real": True}]
    mock_qdrant_result = MagicMock()
    mock_qdrant_result.points = [MagicMock()]
    mock_qdrant_result.points[0].payload = {"user_id": "test-user-123"}

    with patch('server.deps.get_embeddings', return_value=mock_embeddings), \
         patch.dict(os.environ, {"API_URL": "http://test-api.com"}, clear=True):  # Clear AUTH_KEY

        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as client:
            with patch.object(app.state, 'qdrant_client') as mock_qdrant:
                mock_qdrant.query_points.return_value = mock_qdrant_result

                test_image = create_test_image()
                response = await client.post(
                    "/api/verify-face",
                    files={"image": ("test.jpg", test_image, "image/jpeg")},
                    data={"timeLogged": "2025-01-01T12:00:00"}
                )

                assert response.status_code == 500
                data = response.json()
                assert "Auth key not found" in data["message"]


@pytest.mark.asyncio
async def test_verify_face_external_api_error():
    """Test error when external authentication API returns error"""
    mock_embeddings = [{"embedding": [0.1] * 512, "is_real": True}]
    mock_qdrant_result = MagicMock()
    mock_qdrant_result.points = [MagicMock()]
    mock_qdrant_result.points[0].payload = {"user_id": "test-user-123"}

    mock_auth_response = MagicMock()
    mock_auth_response.status_code = 401
    mock_auth_response.text = "Unauthorized"

    with patch('server.deps.get_embeddings', return_value=mock_embeddings), \
         patch.dict(os.environ, {"API_URL": "http://test-api.com", "AUTH_KEY": "test-key"}), \
         patch('httpx.AsyncClient') as mock_httpx:

        mock_client = AsyncMock()
        mock_client.post.return_value = mock_auth_response
        mock_httpx.return_value.__aenter__.return_value = mock_client

        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as client:
            with patch.object(app.state, 'qdrant_client') as mock_qdrant:
                mock_qdrant.query_points.return_value = mock_qdrant_result

                test_image = create_test_image()
                response = await client.post(
                    "/api/verify-face",
                    files={"image": ("test.jpg", test_image, "image/jpeg")},
                    data={"timeLogged": "2025-01-01T12:00:00"}
                )

                assert response.status_code == 401


@pytest.mark.asyncio
async def test_verify_face_invalid_image():
    """Test error when image processing fails"""
    with patch('server.deps.get_embeddings', side_effect=ValueError("Unable to detect face")):
        async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as client:
            test_image = create_test_image()
            response = await client.post(
                "/api/verify-face",
                files={"image": ("test.jpg", test_image, "image/jpeg")},
                data={"timeLogged": "2025-01-01T12:00:00"}
            )

            assert response.status_code == 400
            data = response.json()
            assert "Unable to detect face" in data["message"]


@pytest.mark.asyncio
async def test_verify_face_missing_image():
    """Test error when no image is provided"""
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as client:
        response = await client.post(
            "/api/verify-face",
            data={"timeLogged": "2025-01-01T12:00:00"}
        )

        assert response.status_code == 422


@pytest.mark.asyncio
async def test_verify_face_missing_time_logged():
    """Test error when timeLogged is missing"""
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as client:
        test_image = create_test_image()
        response = await client.post(
            "/api/verify-face",
            files={"image": ("test.jpg", test_image, "image/jpeg")}
        )

        assert response.status_code == 422
