import os
from httpx import ASGITransport, AsyncClient
import pytest

from fastapi.testclient import TestClient
from server.app import app

@pytest.mark.asyncio
async def test_verify_face():
    async with AsyncClient(transport=ASGITransport(app=app), base_url="http://localhost:8000") as asyncClient:
        image_src = os.getcwd() + "/images/sample-face.jpg"

        response = await asyncClient.post(
            "/api/verify-face",
            files={"image": ("test.jpg", open("test.jpg", "rb"), "image/jpeg")},
            data={"timeLogged": "2021-01-01T00:00:00Z"}
        )
        assert response.status_code == 400
