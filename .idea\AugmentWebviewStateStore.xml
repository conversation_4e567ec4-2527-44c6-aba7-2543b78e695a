<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;5a3a5b23-c1e0-4aa2-84c8-f72b81c20e72&quot;,&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[],&quot;conversations&quot;:{&quot;5a3a5b23-c1e0-4aa2-84c8-f72b81c20e72&quot;:{&quot;id&quot;:&quot;5a3a5b23-c1e0-4aa2-84c8-f72b81c20e72&quot;,&quot;name&quot;:&quot;FastAPI Image Blob PIL Handling\n&quot;,&quot;createdAtIso&quot;:&quot;2025-09-29T03:35:28.771Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-09-29T09:36:01.320Z&quot;,&quot;chatHistory&quot;:[{&quot;request_id&quot;:&quot;7d812eec-9c9f-4602-83ae-187aac83b22f&quot;,&quot;uuid&quot;:&quot;a2498fb9-683c-4705-b670-7af24922c49e&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:0,&quot;toTimestamp&quot;:1759116928772,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;63784a58-7dd5-4354-b232-f0de1f594dce&quot;,&quot;timestamp&quot;:&quot;2025-09-29T03:38:32.365Z&quot;,&quot;request_message&quot;:&quot;In FastAPI, how can I handle the form data request with image as blob and open it using PIL package?&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;e2879df6-335b-41a6-8567-9459be092b27&quot;,&quot;timestamp&quot;:&quot;2025-09-29T03:38:41.893Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;4f27414b-c0cb-49ab-a8f5-4d07aa1545f1&quot;,&quot;timestamp&quot;:&quot;2025-09-29T03:38:49.979Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;05869916-d052-4a5a-94a7-151412d3a1d2&quot;,&quot;timestamp&quot;:&quot;2025-09-29T03:39:01.543Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;3bd404e9-728b-45e2-af7a-93245aefb7b3&quot;,&quot;timestamp&quot;:&quot;2025-09-29T03:39:09.606Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;f38d2e40-8bbe-4d3a-9bd0-b7a8273a0b7e&quot;,&quot;timestamp&quot;:&quot;2025-09-29T03:53:29.766Z&quot;,&quot;request_message&quot;:&quot;How can I use the qdrant_client across the @/main.py ? I want to search the embedding in Qdrant&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;136832a2-19f2-4ec2-8b63-89ed88868aac&quot;,&quot;timestamp&quot;:&quot;2025-09-29T03:53:40.979Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;4511755a-8489-44c6-a6af-f20d861b4d29&quot;,&quot;timestamp&quot;:&quot;2025-09-29T04:17:21.248Z&quot;,&quot;request_message&quot;:&quot;Can you fix the warning in line 175 about \&quot;Unpack this comprehension expression\&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;69da2e65-3939-4354-aa07-31204d01e3bd&quot;,&quot;timestamp&quot;:&quot;2025-09-29T04:17:25.022Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;chatItemType&quot;:&quot;exchange-pointer&quot;,&quot;exchangeUuid&quot;:&quot;85143f4c-8090-4b05-9afd-23e5fe6bd79c&quot;,&quot;timestamp&quot;:&quot;2025-09-29T04:17:33.587Z&quot;,&quot;request_message&quot;:&quot;&quot;,&quot;status&quot;:&quot;success&quot;,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;request_id&quot;:&quot;e0128933-8a89-420b-91b7-8234672ba56c&quot;,&quot;uuid&quot;:&quot;850c490a-3e1c-4553-8697-cffb2bb09f9e&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:1759116928772,&quot;toTimestamp&quot;:1759119459224,&quot;seen_state&quot;:&quot;unseen&quot;},{&quot;request_id&quot;:&quot;94de7d32-011f-4ec5-be52-a120c32b48e4&quot;,&quot;uuid&quot;:&quot;b4db2213-37b7-469c-88cb-3cb84f9b6a83&quot;,&quot;chatItemType&quot;:&quot;agentic-checkpoint-delimiter&quot;,&quot;status&quot;:&quot;success&quot;,&quot;fromTimestamp&quot;:1759119459224,&quot;toTimestamp&quot;:1759138561427,&quot;seen_state&quot;:&quot;unseen&quot;}],&quot;feedbackStates&quot;:{&quot;temp-fe-68f5a626-0a74-4cb9-85bb-29945983e1a0&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-c477f8ee-2cf8-4887-9bd0-93bc324b2742&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-06020ef4-e78d-4342-9aaf-1d8177e303c3&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-11df6b75-31f6-47bd-9646-e9c00edb5740&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-e0b322f9-2807-4a68-81a2-b1afe9460abc&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-3d5cc744-dbd0-468d-af77-381965f6bfab&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-051c25a8-c934-4eab-8d0c-06b0be6c4b62&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-776a00bd-9221-4437-8691-c57277845c13&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-0b465d6b-a5cd-46c1-b4f7-fd1ab92e4e4b&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;},&quot;temp-fe-15a506c1-805b-4243-860e-5795ffcff610&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;mentioned_items&quot;:[],&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;isAgentConversation&quot;:true,&quot;hasDirtyEdits&quot;:true,&quot;hasTitleGenerated&quot;:true},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;0e4697a1-2766-4828-a9be-0fd2a9e30cad&quot;}}}" />
      </map>
    </option>
  </component>
</project>